import{W as s,$ as t}from"./index-Cr-caQ2D.js";import{logEvent as c}from"./index.esm-Tgzqn2Pi.js";import"./index.esm2017-H7c5Bkvh.js";const u=async e=>{try{console.log(`Getting contact info for owner ID: ${e}`);const o=await s(e);return o?(console.log("Found owner data:",o),{ownerPhone:o.phone||void 0,ownerName:o.displayName||"Unknown",ownerEmail:o.email||void 0,success:!0,message:"Owner contact information retrieved successfully"}):(console.error(`No user document found for owner ID: ${e}`),{success:!1,message:"Owner information not found",ownerName:"Unknown"})}catch(o){return console.error("Error getting owner contact info:",o),{success:!1,message:"Failed to retrieve owner contact information",ownerName:"Unknown"}}},f=(e,o)=>{try{const n=e.replace(/[\s-\(\)]/g,""),r=encodeURIComponent(o),a=`https://wa.me/${n}?text=${r}`;return window.open(a,"_blank"),t&&c(t,"contact_whatsapp_launched",{phone_number:n}),!0}catch(n){return console.error("Error launching WhatsApp:",n),!1}},w=async(e,o,n,r)=>{try{return console.log(`Would send email to ${e} about interest in book "${o}" from ${n} (${r})`),t&&c(t,"contact_email_notification_sent",{book_title:o}),!0}catch(a){return console.error("Error sending email notification:",a),!1}},d=(e,o,n,r)=>{t&&c(t,"book_owner_contacted",{book_id:e,owner_id:o,user_id:n,contact_method:r,timestamp:new Date().toISOString()})};export{u as getOwnerContactInfo,f as launchWhatsApp,w as sendOwnerEmailNotification,d as trackContactInteraction};
