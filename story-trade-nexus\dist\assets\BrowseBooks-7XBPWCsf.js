import{r as t,J as n,f as C,j as e,H as E,S as R,I as G,h as c,i as g,k as N,l as o,m as P,n as D,L as I,o as Y}from"./index-DXc28CfW.js";const J=()=>{const[r,k]=t.useState(""),[d,w]=t.useState("All"),[m,B]=t.useState("All"),[f,S]=t.useState([]),[x,b]=t.useState(!0),[j,h]=t.useState(null),[l,a]=t.useState(null),A=["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"],F=["All","For Rent","For Sale","For Exchange"];t.useEffect(()=>{y()},[]);const y=async()=>{try{b(!0),h(null),a("loading"),console.log("BrowseBooks: Fetching books from Firebase"),n.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const s=await C(!1);s.some(u=>u.distance!==void 0)?(a("success"),n.success("Books sorted by distance (closest first)",{id:"location-toast",duration:3e3})):(a("error"),n.info("Books sorted by newest first",{id:"location-toast",duration:3e3})),S(s),console.log(`BrowseBooks: Fetched ${s.length} books from Firebase`)}catch(s){console.error("Error fetching books:",s),a("error"),s instanceof Error?(h(`Failed to load books: ${s.message}. Please try again.`),(s.message.includes("permission")||s.message.includes("denied"))&&(a("denied"),n.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):h("Failed to load books. Please try again.")}finally{b(!1)}},p=()=>{console.log("BrowseBooks: Refreshing books"),y()},v=f.filter(s=>{const i=r===""||s.title.toLowerCase().includes(r.toLowerCase())||s.author.toLowerCase().includes(r.toLowerCase()),u=d==="All"||s.genre.includes(d),L=m==="All"||s.availability.includes(m);return i&&u&&L});return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(E,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(R,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(G,{type:"text",placeholder:"Search by title or author...",className:"pl-10",value:r,onChange:s=>k(s.target.value)})]}),e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",value:d,onChange:s=>w(s.target.value),children:A.map(s=>e.jsx("option",{value:s,children:s},s))}),e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",value:m,onChange:s=>B(s.target.value),children:F.map(s=>e.jsx("option",{value:s,children:s},s))})]})}),e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center",children:[l==="loading"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(c,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),l==="success"&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(c,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:"Books sorted by distance (closest first)"})]}),l==="error"&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(c,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:"Books sorted by newest first"})]}),l==="denied"&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(c,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(g,{variant:"outline",onClick:p,disabled:x,className:"text-sm",children:x?e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]}),j&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:j}),e.jsx(g,{variant:"link",onClick:p,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),x?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((s,i)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(o,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(o,{className:"h-6 w-3/4 mb-2"}),e.jsx(o,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(o,{className:"h-8 w-20"}),e.jsx(o,{className:"h-8 w-20"})]})]})]},i))}):v.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:v.map(s=>e.jsx(P,{book:s},s.id))}):f.length===0?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(D,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(I,{to:"/add-books",children:e.jsx(g,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(Y,{})]})};export{J as default};
