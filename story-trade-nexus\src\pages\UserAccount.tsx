import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  BookOpen,
  Heart,
  Edit,
  Save,
  X,
  Home,
  Building,
  Map,
  Plus,
  LayoutDashboard,
  Settings,
  BookMarked,
  Clock,
  LogOut
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';
import { useAuth } from '@/lib/AuthContext';
import MainLayout from '@/components/layouts/MainLayout';
import { getBooksByOwner } from '@/lib/bookService';
import { Book, BookApprovalStatus } from '@/types';
import { updateUserDocument } from '@/lib/userService';
import BookCard from '@/components/BookCard';
import { debouncedFetchCommunitiesByPincode } from '@/lib/communityUtils';
import { Combobox, ComboboxOption } from '@/components/ui/combobox';

// Define tab types
type TabType = 'dashboard' | 'profile' | 'books' | 'settings';

const UserAccount = () => {
  const { currentUser, userData, refreshUserData, signOut } = useAuth();
  const [userBooks, setUserBooks] = useState<Book[]>([]);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const navigate = useNavigate();
  const location = useLocation();
  const [formData, setFormData] = useState({
    displayName: '',
    phone: '',
    address: '',
    apartment: '',
    city: '',
    state: '',
    pincode: '',
    community: '',
    customCommunity: ''
  });

  // Community-related state
  const [communities, setCommunities] = useState<string[]>([]);
  const [communityOptions, setCommunityOptions] = useState<ComboboxOption[]>([]);
  const [isLoadingCommunities, setIsLoadingCommunities] = useState(false);
  const [communityError, setCommunityError] = useState<string | null>(null);

  // Set active tab based on URL path
  useEffect(() => {
    const path = location.pathname;
    if (path.includes('profile')) {
      setActiveTab('profile');
    } else if (path.includes('books')) {
      setActiveTab('books');
    } else if (path.includes('settings')) {
      setActiveTab('settings');
    } else {
      setActiveTab('dashboard');
    }
  }, [location.pathname]);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Fetch user's books
        const books = await getBooksByOwner(currentUser.uid);
        setUserBooks(books);

        // Initialize form data with current user data
        if (userData) {
          setFormData({
            displayName: userData.displayName || '',
            phone: userData.phone || '',
            address: userData.address || '',
            apartment: userData.apartment || '',
            city: userData.city || '',
            state: userData.state || '',
            pincode: userData.pincode || '',
            community: userData.community || '',
            customCommunity: ''
          });

          // If user has a pincode, fetch communities for that pincode
          if (userData.pincode && userData.pincode.length === 6) {
            fetchCommunitiesForPincode(userData.pincode);
          }
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.error('Failed to load profile data');
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [currentUser, userData]);

  // Format date to readable format
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);

    return new Intl.DateTimeFormat('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  // Fetch communities based on pincode
  const fetchCommunitiesForPincode = async (pincode: string) => {
    if (pincode.length !== 6) {
      setCommunities([]);
      setCommunityOptions([]);
      return;
    }

    setIsLoadingCommunities(true);
    setCommunityError(null);

    try {
      const fetchedCommunities = await debouncedFetchCommunitiesByPincode(pincode);
      setCommunities(fetchedCommunities);

      // Create options for the combobox
      const options: ComboboxOption[] = fetchedCommunities.map(community => ({
        value: community,
        label: community
      }));

      // Add "Other" option
      options.push({
        value: 'Other',
        label: 'Other (specify below)'
      });

      setCommunityOptions(options);
      console.log(`Profile: Found ${fetchedCommunities.length} communities for pincode ${pincode}`);
    } catch (error) {
      console.error('Error fetching communities:', error);
      setCommunityError('Failed to load communities for this pincode');
      setCommunities([]);
      setCommunityOptions([]);
    } finally {
      setIsLoadingCommunities(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // If pincode is changed, fetch communities
    if (name === 'pincode' && value.length === 6) {
      fetchCommunitiesForPincode(value);
    } else if (name === 'pincode' && value.length !== 6) {
      setCommunities([]);
      setCommunityOptions([]);
    }
  };

  // Handle community selection
  const handleCommunityChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      community: value,
      customCommunity: value === 'Other' ? prev.customCommunity : ''
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser) return;

    try {
      // Prepare the data to be saved
      const dataToSave = {
        ...formData,
        // Use custom community if "Other" is selected, otherwise use selected community
        community: formData.community === 'Other' ? formData.customCommunity : formData.community
      };

      // Remove customCommunity from the data to be saved
      const { customCommunity, ...finalData } = dataToSave;

      console.log('Profile: Updating user data with community:', finalData.community);
      await updateUserDocument(currentUser.uid, finalData);
      await refreshUserData();
      toast.success('Profile updated successfully');
      setEditMode(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    }
  };

  // Cancel edit mode
  const handleCancel = () => {
    // Reset form data to current user data
    if (userData) {
      setFormData({
        displayName: userData.displayName || '',
        phone: userData.phone || '',
        address: userData.address || '',
        apartment: userData.apartment || '',
        city: userData.city || '',
        state: userData.state || '',
        pincode: userData.pincode || '',
        community: userData.community || '',
        customCommunity: ''
      });

      // Reset community options if user has a pincode
      if (userData.pincode && userData.pincode.length === 6) {
        fetchCommunitiesForPincode(userData.pincode);
      } else {
        setCommunities([]);
        setCommunityOptions([]);
      }
    }
    setEditMode(false);
  };

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);

    // Update URL without full page reload
    switch (tab) {
      case 'profile':
        navigate('/profile');
        break;
      case 'books':
        navigate('/my-books');
        break;
      case 'settings':
        navigate('/settings');
        break;
      default:
        navigate('/dashboard');
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/');
      toast.success('Signed out successfully');
    } catch (error) {
      console.error('Error signing out:', error);
      toast.error('Failed to sign out');
    }
  };

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (userData?.displayName) {
      return userData.displayName
        .split(" ")
        .map((n) => n[0])
        .join("")
        .toUpperCase()
        .substring(0, 2);
    }
    return currentUser?.email?.substring(0, 2).toUpperCase() || "U";
  };

  // Get display name
  const displayName = userData?.displayName || currentUser?.displayName || currentUser?.email?.split('@')[0] || 'Reader';

  // Get user email
  const email = userData?.email || currentUser?.email || 'No email provided';

  // Get book counts
  const booksAdded = userBooks.length || 0;
  const approvedBooks = userBooks.filter(book =>
    book.approvalStatus === BookApprovalStatus.Approved || !book.approvalStatus).length;
  const pendingCount = userBooks.filter(book =>
    book.approvalStatus === BookApprovalStatus.Pending).length;

  if (loading) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="md:w-1/4">
                <Skeleton className="h-40 w-40 rounded-full mx-auto" />
                <div className="mt-4 space-y-2">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
              <div className="md:w-3/4 space-y-6">
                <Skeleton className="h-8 w-1/2" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!currentUser || !userData) {
    return (
      <MainLayout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <h1 className="text-2xl font-bold text-navy-800 mb-4">
              User Not Found
            </h1>
            <p className="text-gray-600 mb-6">
              Please sign in to view your account.
            </p>
            <Link to="/signin">
              <Button>
                Sign In
              </Button>
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="flex flex-col md:flex-row">
            {/* Left Sidebar with Tabs */}
            <div className="md:w-64 bg-gray-50 p-6 border-r border-gray-200">
              <div className="flex flex-col items-center mb-6">
                <Avatar className="h-20 w-20">
                  <AvatarImage
                    src={userData.photoURL || ""}
                    alt={userData.displayName || "User"}
                  />
                  <AvatarFallback className="text-xl bg-burgundy-100 text-burgundy-700">
                    {getInitials()}
                  </AvatarFallback>
                </Avatar>
                <h2 className="mt-4 text-lg font-semibold text-center">
                  {userData.displayName}
                </h2>
                <p className="text-sm text-gray-600 text-center">{userData.email}</p>
              </div>

              <nav className="space-y-1">
                <button
                  onClick={() => handleTabChange('dashboard')}
                  className={`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                    activeTab === 'dashboard'
                      ? 'bg-burgundy-100 text-burgundy-700 font-medium'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <LayoutDashboard className="h-4 w-4 mr-3" />
                  Dashboard
                </button>
                <button
                  onClick={() => handleTabChange('profile')}
                  className={`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                    activeTab === 'profile'
                      ? 'bg-burgundy-100 text-burgundy-700 font-medium'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <User className="h-4 w-4 mr-3" />
                  Profile
                </button>
                <button
                  onClick={() => handleTabChange('books')}
                  className={`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                    activeTab === 'books'
                      ? 'bg-burgundy-100 text-burgundy-700 font-medium'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <BookOpen className="h-4 w-4 mr-3" />
                  My Books
                </button>
                <button
                  onClick={() => handleTabChange('settings')}
                  className={`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                    activeTab === 'settings'
                      ? 'bg-burgundy-100 text-burgundy-700 font-medium'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Settings className="h-4 w-4 mr-3" />
                  Settings
                </button>
              </nav>

              <div className="mt-auto pt-6 border-t border-gray-200 mt-6">
                <button
                  onClick={handleSignOut}
                  className="w-full flex items-center px-3 py-2 text-sm rounded-md text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <LogOut className="h-4 w-4 mr-3" />
                  Sign Out
                </button>
              </div>
            </div>

            {/* Main Content Area */}
            <div className="flex-1 p-6">
              {/* Dashboard Tab */}
              {activeTab === 'dashboard' && (
                <div>
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <div>
                      <h1 className="text-2xl font-bold text-navy-800 mb-2">
                        Welcome, {displayName}!
                      </h1>
                      <p className="text-gray-600">Manage your books and exchanges</p>
                    </div>
                    <div className="mt-4 md:mt-0">
                      <Link to="/add-books">
                        <Button className="flex items-center gap-2">
                          <Plus className="h-4 w-4" />
                          Add New Books
                        </Button>
                      </Link>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div className="bg-gray-50 p-4 rounded-md shadow-sm">
                      <div className="text-3xl font-bold text-burgundy-600">{booksAdded}</div>
                      <div className="text-sm text-gray-600">Total Books</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-md shadow-sm">
                      <div className="text-3xl font-bold text-green-600">{approvedBooks}</div>
                      <div className="text-sm text-gray-600">Active Listings</div>
                    </div>
                    <div className="bg-gray-50 p-4 rounded-md shadow-sm">
                      <div className="text-3xl font-bold text-amber-600">{pendingCount}</div>
                      <div className="text-sm text-gray-600">Pending Approval</div>
                    </div>
                  </div>

                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-xl font-bold text-navy-800">Your Books</h2>
                      <Button
                        variant="link"
                        className="text-burgundy-600"
                        onClick={() => handleTabChange('books')}
                      >
                        View All
                      </Button>
                    </div>

                    {userBooks.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        {userBooks.slice(0, 4).map((book) => (
                          <BookCard key={book.id} book={book} />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="text-gray-500 mb-4">You haven't added any books yet</div>
                        <Link to="/add-books">
                          <Button>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Your First Book
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Profile Tab */}
              {activeTab === 'profile' && (
                <div>
                  <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold text-navy-800">My Profile</h1>
                    {!editMode ? (
                      <Button onClick={() => setEditMode(true)} className="flex items-center gap-2">
                        <Edit className="h-4 w-4" />
                        Edit Profile
                      </Button>
                    ) : (
                      <div className="flex gap-2">
                        <Button variant="outline" onClick={handleCancel} className="flex items-center gap-2">
                          <X className="h-4 w-4" />
                          Cancel
                        </Button>
                        <Button onClick={handleSubmit} className="flex items-center gap-2">
                          <Save className="h-4 w-4" />
                          Save Changes
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className="bg-gray-50 rounded-lg p-5 mb-6">
                    <h3 className="font-medium text-navy-800 mb-4 flex items-center">
                      <User className="h-5 w-5 mr-2 text-burgundy-500" />
                      Personal Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-500">Email</p>
                        <div className="flex items-center mt-1">
                          <Mail className="h-4 w-4 text-gray-400 mr-2" />
                          <p>{userData.email}</p>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Phone</p>
                        <div className="flex items-center mt-1">
                          <Phone className="h-4 w-4 text-gray-400 mr-2" />
                          {editMode ? (
                            <Input
                              name="phone"
                              value={formData.phone}
                              onChange={handleInputChange}
                              placeholder="Enter phone number"
                            />
                          ) : (
                            <p>{userData.phone || 'Not provided'}</p>
                          )}
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Member Since</p>
                        <div className="flex items-center mt-1">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          <p>{formatDate(userData.createdAt)}</p>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">Display Name</p>
                        <div className="flex items-center mt-1">
                          <User className="h-4 w-4 text-gray-400 mr-2" />
                          {editMode ? (
                            <Input
                              name="displayName"
                              value={formData.displayName}
                              onChange={handleInputChange}
                              placeholder="Enter display name"
                            />
                          ) : (
                            <p>{userData.displayName || 'Not provided'}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-5">
                    <h3 className="font-medium text-navy-800 mb-4 flex items-center">
                      <MapPin className="h-5 w-5 mr-2 text-burgundy-500" />
                      Address Information
                    </h3>
                    {editMode ? (
                      <form className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <label className="text-sm text-gray-500">Address</label>
                          <Input
                            name="address"
                            value={formData.address}
                            onChange={handleInputChange}
                            placeholder="Enter your address"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <label className="text-sm text-gray-500">City</label>
                          <Input
                            name="city"
                            value={formData.city}
                            onChange={handleInputChange}
                            placeholder="Enter city"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <label className="text-sm text-gray-500">State</label>
                          <Input
                            name="state"
                            value={formData.state}
                            onChange={handleInputChange}
                            placeholder="Enter state"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <label className="text-sm text-gray-500">Pincode</label>
                          <Input
                            name="pincode"
                            value={formData.pincode}
                            onChange={handleInputChange}
                            placeholder="Enter pincode"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <label className="text-sm text-gray-500">
                            Community
                            <span className="text-xs text-blue-600 ml-1">(affects book discovery)</span>
                          </label>
                          {communityOptions.length > 0 ? (
                            <div className="mt-1">
                              <Combobox
                                options={communityOptions}
                                value={formData.community}
                                onValueChange={handleCommunityChange}
                                placeholder="Select your community"
                                emptyText="No communities found"
                                disabled={isLoadingCommunities}
                              />
                              {isLoadingCommunities && (
                                <p className="text-xs text-gray-500 mt-1">Loading communities...</p>
                              )}
                              {communityError && (
                                <p className="text-xs text-red-500 mt-1">{communityError}</p>
                              )}
                              {formData.community === 'Other' && (
                                <Input
                                  name="customCommunity"
                                  value={formData.customCommunity}
                                  onChange={handleInputChange}
                                  placeholder="Enter your community name"
                                  className="mt-2"
                                />
                              )}
                            </div>
                          ) : (
                            <div className="mt-1">
                              <Input
                                name="community"
                                value={formData.community}
                                onChange={handleInputChange}
                                placeholder="Enter your community name"
                                className="mt-1"
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                Enter a 6-digit pincode above to see available communities
                              </p>
                            </div>
                          )}
                        </div>
                        <div>
                          <label className="text-sm text-gray-500">Apartment/Building</label>
                          <Input
                            name="apartment"
                            value={formData.apartment}
                            onChange={handleInputChange}
                            placeholder="Enter apartment or building name"
                            className="mt-1"
                          />
                        </div>
                      </form>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="md:col-span-2">
                          <p className="text-sm text-gray-500">Address</p>
                          <div className="flex items-start mt-1">
                            <Home className="h-4 w-4 text-gray-400 mr-2 mt-1" />
                            <p>{userData.address || 'Not provided'}</p>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">City</p>
                          <p className="mt-1">{userData.city || 'Not provided'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">State</p>
                          <p className="mt-1">{userData.state || 'Not provided'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Pincode</p>
                          <p className="mt-1">{userData.pincode || 'Not provided'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Community</p>
                          <div className="flex items-start mt-1">
                            <Home className="h-4 w-4 text-gray-400 mr-2 mt-1" />
                            <div>
                              <p>{userData.community || 'Not provided'}</p>
                              {userData.community && (
                                <p className="text-xs text-blue-600 mt-1">
                                  Books from your community appear first in Browse Books
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Apartment/Building</p>
                          <div className="flex items-start mt-1">
                            <Building className="h-4 w-4 text-gray-400 mr-2 mt-1" />
                            <p>{userData.apartment || 'Not provided'}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* My Books Tab */}
              {activeTab === 'books' && (
                <div>
                  <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold text-navy-800">My Books</h1>
                    <Link to="/add-books">
                      <Button className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        Add New Book
                      </Button>
                    </Link>
                  </div>

                  {userBooks.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                      {userBooks.map((book) => (
                        <BookCard key={book.id} book={book} />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-gray-500 mb-4">You haven't added any books yet</div>
                      <Link to="/add-books">
                        <Button>
                          <Plus className="h-4 w-4 mr-2" />
                          Add Your First Book
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              )}

              {/* Settings Tab */}
              {activeTab === 'settings' && (
                <div>
                  <h1 className="text-2xl font-bold text-navy-800 mb-6">Account Settings</h1>

                  <div className="bg-gray-50 rounded-lg p-5 mb-6">
                    <h3 className="font-medium text-navy-800 mb-4">Notification Preferences</h3>
                    <p className="text-gray-500 mb-4">Coming soon! You'll be able to customize your notification preferences here.</p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-5 mb-6">
                    <h3 className="font-medium text-navy-800 mb-4">Privacy Settings</h3>
                    <p className="text-gray-500 mb-4">Coming soon! You'll be able to manage your privacy settings here.</p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-5">
                    <h3 className="font-medium text-navy-800 mb-4 text-red-600">Danger Zone</h3>
                    <p className="text-gray-500 mb-4">These actions are irreversible. Please proceed with caution.</p>
                    <Button variant="destructive" disabled>
                      Delete Account
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default UserAccount;
