import{z as l,r as o,u as p,q as y,j as e,M as i,s as c,L as d,a as m,t as b,v as w,w as N,x as v,y as k,I as F,A as S,D as P,J as x}from"./index-DXc28CfW.js";const I=l.object({email:l.string().email({message:"Please enter a valid email address"})}),E=()=>{const[t,n]=o.useState(!1),[u,h]=o.useState(!1),{resetPassword:j}=p(),a=y({resolver:P(I),defaultValues:{email:""}}),g=async s=>{n(!0);try{console.log("Reset password for:",s.email),await j(s.email),x.success("Password reset link sent! Check your email inbox."),h(!0)}catch(r){console.error("Password reset error:",r);const f=r instanceof Error?r.message:"Failed to send reset link";x.error(f)}finally{n(!1)}};return u?e.jsx(i,{children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:e.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto bg-green-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:e.jsx(c,{className:"h-8 w-8 text-green-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Check Your Email"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"We've sent a password reset link to your email address. Please check your inbox and follow the instructions."}),e.jsx(d,{to:"/signin",children:e.jsx(m,{variant:"link",children:"Back to Sign In"})})]})})})}):e.jsx(i,{children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Forgot Password"}),e.jsx("p",{className:"text-gray-600",children:"Enter your email to receive a password reset link"})]}),e.jsx(b,{...a,children:e.jsxs("form",{onSubmit:a.handleSubmit(g),className:"space-y-6",children:[e.jsx(w,{control:a.control,name:"email",render:({field:s})=>e.jsxs(N,{children:[e.jsx(v,{children:"Email"}),e.jsx(k,{children:e.jsx(F,{placeholder:"<EMAIL>",type:"email",disabled:t,...s})}),e.jsx(S,{})]})}),e.jsxs(m,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:t,children:[e.jsx(c,{className:"h-4 w-4"}),"Send Reset Link"]})]})}),e.jsx("div",{className:"text-center mt-6",children:e.jsxs("p",{className:"text-gray-600",children:["Remember your password? "," ",e.jsx(d,{to:"/signin",className:"text-burgundy-500 hover:underline font-medium",children:"Sign In"})]})})]})})})};export{E as default};
