import{u as N,r as l,E as w,J as a,j as e,M as E,s as b,a as u,k as g,L as V}from"./index-Cr-caQ2D.js";const I=()=>{const{currentUser:o,emailVerified:h,sendVerificationEmail:j,reloadUser:m,signOut:v}=N(),[r,d]=l.useState(!1),[i,f]=l.useState(!1),[c,x]=l.useState(0),t=w();l.useEffect(()=>{if(!o){t("/signin");return}if(h){t("/");return}const s=setInterval(async()=>{try{await m()&&(a.success("Email verified successfully!"),t("/"))}catch(n){console.error("Error checking verification status:",n)}},5e3);return()=>clearInterval(s)},[o,h,t,m]),l.useEffect(()=>{if(c>0){const s=setTimeout(()=>x(c-1),1e3);return()=>clearTimeout(s)}else c===0&&i&&f(!1)},[c,i]);const y=async()=>{if(!i){d(!0);try{await j(),a.success("Verification email sent! Please check your inbox."),f(!0),x(60)}catch(s){console.error("Error sending verification email:",s);const n=s instanceof Error?s.message:"Failed to send verification email";a.error(n)}finally{d(!1)}}},p=async()=>{d(!0);try{await m()?(a.success("Email verified successfully! Redirecting to dashboard..."),setTimeout(()=>t("/dashboard"),1500)):a.info("Your email is not verified yet. Please check your inbox and click the verification link.")}catch(s){console.error("Error checking verification status:",s);const n=s instanceof Error?s.message:"Failed to check verification status";a.error(n)}finally{d(!1)}},k=async()=>{try{await v(),t("/signin")}catch(s){console.error("Error signing out:",s),a.error("Failed to sign out")}};return e.jsx(E,{children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:e.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto bg-blue-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:e.jsx(b,{className:"h-8 w-8 text-blue-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Verify Your Email"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["We've sent a verification email to ",e.jsx("strong",{children:o==null?void 0:o.email}),". Please check your inbox and click the verification link to activate your account."]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:[e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Important:"})," You must verify your email before you can access all features. This helps ensure the security of your account and allows us to contact you about your books."]}),e.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[e.jsx("strong",{children:"Note:"})," We're automatically checking your verification status every few seconds. Once you click the verification link in your email, you'll be redirected automatically."]}),e.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[e.jsx("strong",{children:"While waiting for verification:"})," You can still browse books, view book details, and access public pages, but you won't be able to add books or access your dashboard until your email is verified."]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(u,{onClick:y,disabled:r||i,className:"w-full flex items-center justify-center gap-2",children:[r&&i?e.jsx(g,{className:"h-4 w-4 animate-spin"}):e.jsx(b,{className:"h-4 w-4"}),i?`Resend Email (${c}s)`:r&&!i?"Sending...":"Resend Verification Email"]}),e.jsxs(u,{onClick:p,variant:"secondary",disabled:r,className:"w-full flex items-center justify-center gap-2",children:[e.jsx(g,{className:`h-4 w-4 ${r&&!i?"animate-spin":""}`}),r&&!i?"Checking...":"I've Verified My Email"]}),e.jsx(V,{to:"/",children:e.jsx(u,{variant:"secondary",className:"w-full mb-2",children:"Continue Browsing Books"})}),e.jsx(u,{variant:"outline",onClick:k,className:"w-full",children:"Sign Out"})]}),e.jsx("div",{className:"mt-6 text-sm text-gray-500",children:e.jsxs("p",{children:["Didn't receive the email? Check your spam folder or"," ",e.jsx("button",{onClick:y,disabled:i,className:"text-burgundy-500 hover:underline",children:"click here to resend"}),"."]})}),e.jsxs("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Troubleshooting Tips:"}),e.jsxs("ul",{className:"text-xs text-gray-600 space-y-1 list-disc pl-5",children:[e.jsx("li",{children:"Check your spam or junk folder"}),e.jsxs("li",{children:["Add ",e.jsx("span",{className:"font-mono",children:"<EMAIL>"})," to your contacts"]}),e.jsx("li",{children:'If using Gmail, check the "Promotions" or "Updates" tabs'}),e.jsx("li",{children:"Try using a different browser or device to verify"}),e.jsxs("li",{children:["If you're still having issues, contact support at ",e.jsx("a",{href:"mailto:<EMAIL>",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"})]})]})]})]})})})})};export{I as default};
